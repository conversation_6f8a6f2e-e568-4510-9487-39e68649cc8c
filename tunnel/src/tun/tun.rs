use std::sync::Arc;

use futures::{SinkExt, StreamExt};
use futures_util::stream::{SplitSink, SplitStream};
use log::{debug, error, info};
use netstack_lwip::NetStack;
use tokio::sync::{Mutex, RwLock};

use crate::context::context::TunnelContext;

use super::config::TunConfig;
use super::error::{TunError, TunResult};
use super::metrics::TunMetrics;
use super::netstack_manager::NetStackManager;
use super::tcp_handler::TcpHandler;
use super::udp_handler::UdpHandler;


/// TUN 设备结构体
///
/// 重构后的 TUN 设备实现，采用模块化设计，职责分离。
/// 主要负责协调各个组件的工作，而不是直接处理所有逻辑。
/// 集成了性能监控和错误处理机制。
/// 使用直接方法调用替代通道机制，提高性能并简化代码结构。
pub struct Tun {
    /// 隧道上下文，包含隧道相关的配置和状态
    tunnel_context: Arc<TunnelContext>,
    /// TUN 配置
    config: TunConfig,
    /// 网络栈管理器
    netstack_manager: NetStackManager,
    /// TCP 处理器
    tcp_handler: TcpHandler,
    /// UDP 处理器
    udp_handler: UdpHandler,
    /// 性能监控指标
    metrics: Arc<TunMetrics>,
    /// 网络栈发送端，用于向网络栈发送数据
    netstack_sink: Arc<Mutex<Option<SplitSink<std::pin::Pin<Box<NetStack>>, Vec<u8>>>>>,
    /// 网络栈接收端，用于从网络栈接收数据
    netstack_stream: Arc<Mutex<Option<SplitStream<std::pin::Pin<Box<NetStack>>>>>>,
}

impl Tun {
    /// 创建新的 TUN 实例
    ///
    /// # 参数
    /// * `tunnel_context` - 隧道上下文，包含隧道相关的配置和状态
    ///
    /// # 返回值
    /// 返回一个新的 TUN 实例，如果创建失败则返回错误
    pub async fn new(tunnel_context: Arc<TunnelContext>) -> TunResult<Self> {
        Self::with_config(tunnel_context, TunConfig::default()).await
    }

    /// 使用指定配置创建新的 TUN 实例
    ///
    /// # 参数
    /// * `tunnel_context` - 隧道上下文
    /// * `config` - TUN 配置
    ///
    /// # 返回值
    /// 返回配置好的 TUN 实例，如果创建失败则返回错误
    pub async fn with_config(tunnel_context: Arc<TunnelContext>, config: TunConfig) -> TunResult<Self> {
        info!("创建 TUN 实例，配置: {:?}", config);

        // 创建性能监控指标
        let metrics = TunMetrics::new();

        // 验证配置
        let netstack_manager = NetStackManager::new(config.clone());
        if let Err(e) = netstack_manager.validate_config() {
            metrics.record_error("configuration");
            return Err(e);
        }

        // 创建处理器
        let tcp_handler = TcpHandler::new(tunnel_context.clone(), config.clone());
        let udp_handler = UdpHandler::new(tunnel_context.clone(), config.clone());

        let mut tun = Tun {
            tunnel_context,
            config: config.clone(),
            netstack_manager,
            tcp_handler,
            udp_handler,
            metrics: metrics.clone(),
            netstack_sink: Arc::new(Mutex::new(None)),
            netstack_stream: Arc::new(Mutex::new(None)),
        };

        // 启动性能监控
        TunMetrics::start_collection(metrics.clone()).await;

        // 启动 TUN 数据处理器
        if let Err(e) = tun.start_data_processing().await {
            metrics.record_error("netstack_init");
            return Err(e);
        }

        info!("TUN 实例创建成功");
        Ok(tun)
    }

    /// 启动数据处理任务
    ///
    /// 这是重构后的核心方法，将原来的复杂逻辑分解为更小的、职责单一的组件。
    /// 使用直接方法调用替代通道机制，提高性能并简化代码结构。
    ///
    /// # 返回值
    /// 如果启动成功则返回 `Ok(())`，否则返回错误
    async fn start_data_processing(&mut self) -> TunResult<()> {
        debug!("启动 TUN 数据处理任务");

        // 创建网络栈实例
        let netstack = self.netstack_manager.create_netstack()?;

        // 启动网络栈数据处理任务并获取组件
        let (tcp_listener, udp_socket, stack_sink, stack_stream) = self
            .netstack_manager
            .start_data_processing(netstack)
            .await?;

        // 存储网络栈组件以供直接访问
        *self.netstack_sink.lock().await = Some(stack_sink);
        *self.netstack_stream.lock().await = Some(stack_stream);

        // 启动 TCP 处理任务
        self.tcp_handler.start_handling(tcp_listener).await?;

        // 启动 UDP 处理任务
        self.udp_handler.start_handling(udp_socket).await?;

        debug!("所有数据处理任务启动完成");
        Ok(())
    }


    /// 获取需要发送到 TUN 网卡的数据
    ///
    /// 直接从网络栈接收数据，无需通过通道中转
    ///
    /// # 返回值
    /// 返回从网络栈接收到的数据包，如果没有数据则返回空向量
    pub async fn get_tun_data(&self) -> Vec<u8> {
        let mut stream_guard = self.netstack_stream.lock().await;
        if let Some(stream) = stream_guard.as_mut() {
            if let Some(pkt_result) = stream.next().await {
                match pkt_result {
                    Ok(pkt) => pkt,
                    Err(e) => {
                        error!("从网络栈接收数据包时出错: {}", e);
                        vec![]
                    }
                }
            } else {
                vec![]
            }
        } else {
            vec![]
        }
    }

    /// 处理 TUN 数据包
    ///
    /// 直接将数据发送到网络栈，无需通过通道中转
    ///
    /// # 参数
    /// * `data` - 要处理的数据包
    ///
    /// # 返回值
    /// 如果发送成功则返回 `Ok(())`，否则返回错误
    pub async fn handle_tun_data(&self, data: Vec<u8>) -> TunResult<()> {
        let mut sink_guard = self.netstack_sink.lock().await;
        if let Some(sink) = sink_guard.as_mut() {
            sink.send(data)
                .await
                .map_err(|e| TunError::NetworkError(format!("向网络栈发送数据失败: {}", e)))?;
            Ok(())
        } else {
            Err(TunError::NetworkError("网络栈发送端不可用".to_string()))
        }
    }

    /// 处理 TUN 数据包（别名方法）
    ///
    /// 为了保持向后兼容性而提供的别名方法
    ///
    /// # 参数
    /// * `data` - 要处理的数据包
    pub async fn handler_tun_data(&self, data: Vec<u8>) {
        let _ = self.handle_tun_data(data).await;
    }

    /// 获取 TUN 配置
    ///
    /// # 返回值
    /// 返回当前的 TUN 配置
    pub fn config(&self) -> &TunConfig {
        &self.config
    }

    /// 获取网络栈统计信息
    ///
    /// # 返回值
    /// 返回网络栈的统计信息
    pub fn get_netstack_stats(&self) -> super::netstack_manager::NetStackStats {
        self.netstack_manager.get_stats()
    }

    /// 检查地址是否被封锁
    ///
    /// # 参数
    /// * `address` - 要检查的地址
    ///
    /// # 返回值
    /// 如果地址被封锁则返回 `true`，否则返回 `false`
    pub fn is_address_blocked(&self, address: &str) -> bool {
        self.config.is_dns_address_blocked(address)
    }

    /// 更新配置
    ///
    /// # 参数
    /// * `config` - 新的配置
    ///
    /// # 返回值
    /// 如果更新成功则返回 `Ok(())`，否则返回错误
    pub fn update_config(&mut self, config: TunConfig) -> TunResult<()> {
        // 验证新配置
        let temp_manager = NetStackManager::new(config.clone());
        if let Err(e) = temp_manager.validate_config() {
            self.metrics.record_error("configuration");
            return Err(e);
        }

        // 更新配置
        self.config = config.clone();
        self.netstack_manager.update_config(config.clone());
        self.tcp_handler.update_config(config.clone());
        self.udp_handler.update_config(config);

        info!("TUN 配置更新成功");
        Ok(())
    }

    /// 获取性能监控指标
    ///
    /// # 返回值
    /// 返回性能监控指标的引用
    pub fn metrics(&self) -> &Arc<TunMetrics> {
        &self.metrics
    }

    /// 获取统计摘要
    ///
    /// # 返回值
    /// 返回包含主要统计信息的字符串
    pub fn get_stats_summary(&self) -> String {
        self.metrics.get_summary()
    }

    /// 记录错误
    ///
    /// # 参数
    /// * `error_type` - 错误类型
    pub fn record_error(&self, error_type: &str) {
        self.metrics.record_error(error_type);
    }

    /// 健康检查
    ///
    /// # 返回值
    /// 如果系统健康则返回 `true`，否则返回 `false`
    pub fn health_check(&self) -> bool {
        // 检查各种健康指标
        let tcp_total = self.metrics.tcp_stats.total_connections.load(std::sync::atomic::Ordering::Relaxed);
        let tcp_failed = self.metrics.tcp_stats.failed_connections.load(std::sync::atomic::Ordering::Relaxed);
        let udp_total = self.metrics.udp_stats.total_packets.load(std::sync::atomic::Ordering::Relaxed);
        let udp_failed = self.metrics.udp_stats.failed_packets.load(std::sync::atomic::Ordering::Relaxed);

        // 如果失败率超过 50%，认为不健康
        let tcp_healthy = tcp_total == 0 || (tcp_failed * 2 <= tcp_total);
        let udp_healthy = udp_total == 0 || (udp_failed * 2 <= udp_total);

        tcp_healthy && udp_healthy
    }
}
