//! # TUN 配置模块
//!
//! 本模块定义了 TUN 设备相关的配置常量和参数，
//! 包括 DNS 封锁地址、缓冲区大小等配置项。

use std::collections::HashSet;
use std::sync::LazyLock;



/// 网络栈 TCP 缓冲区大小
///
/// 控制网络栈中 TCP 连接的缓冲区大小
pub const NETSTACK_TCP_BUFFER_SIZE: usize = 512;

/// 网络栈 UDP 缓冲区大小
///
/// 控制网络栈中 UDP 连接的缓冲区大小
pub const NETSTACK_UDP_BUFFER_SIZE: usize = 256;

/// 封锁的 DNS 加密地址列表
///
/// 这些地址使用 DNS over TLS (DoT) 或 DNS over HTTPS (DoH) 协议，
/// 为了确保 DNS 查询通过本地代理处理，需要封锁这些地址
const BLOCKED_DNS_ADDRESSES_ARRAY: [&str; 12] = [
    "*******:853",          // Cloudflare DoT
    "*******:443",          // Cloudflare DoH
    "*******:853",          // Cloudflare DoT (备用)
    "*******:443",          // Cloudflare DoH (备用)
    "*******:853",          // Google DoT
    "*******:443",          // Google DoH
    "***************:853",  // 114 DNS DoT
    "***************:443",  // 114 DNS DoH
    "*********:443",        // 阿里 DNS DoH
    "*********:853",        // 阿里 DNS DoT
    "*********:443",        // 阿里 DNS DoH (备用)
    "*********:853",        // 阿里 DNS DoT (备用)
];

/// 封锁的 DNS 加密地址集合
///
/// 使用 LazyLock 和 HashSet 提供高效的地址查找性能
pub static BLOCKED_DNS_ADDRESSES: LazyLock<HashSet<&'static str>> = LazyLock::new(|| {
    BLOCKED_DNS_ADDRESSES_ARRAY.iter().copied().collect()
});

/// TUN 配置结构体
///
/// 包含 TUN 设备的所有配置参数，支持运行时配置
#[derive(Debug, Clone)]
pub struct TunConfig {
    /// 网络栈 TCP 缓冲区大小
    pub netstack_tcp_buffer_size: usize,
    /// 网络栈 UDP 缓冲区大小
    pub netstack_udp_buffer_size: usize,
    /// 是否启用 DNS 封锁
    pub enable_dns_blocking: bool,
    /// 自定义封锁的 DNS 地址列表
    pub custom_blocked_dns_addresses: Vec<String>,
}

impl Default for TunConfig {
    /// 创建默认的 TUN 配置
    ///
    /// # 返回值
    /// 返回包含默认配置参数的 `TunConfig` 实例
    fn default() -> Self {
        Self {
            netstack_tcp_buffer_size: NETSTACK_TCP_BUFFER_SIZE,
            netstack_udp_buffer_size: NETSTACK_UDP_BUFFER_SIZE,
            enable_dns_blocking: true,
            custom_blocked_dns_addresses: Vec::new(),
        }
    }
}

impl TunConfig {
    /// 创建新的 TUN 配置
    ///
    /// # 返回值
    /// 返回默认配置的 `TunConfig` 实例
    pub fn new() -> Self {
        Self::default()
    }



    /// 设置网络栈缓冲区大小
    ///
    /// # 参数
    /// * `tcp_size` - TCP 缓冲区大小
    /// * `udp_size` - UDP 缓冲区大小
    ///
    /// # 返回值
    /// 返回修改后的配置实例，支持链式调用
    pub fn with_netstack_buffer_size(mut self, tcp_size: usize, udp_size: usize) -> Self {
        self.netstack_tcp_buffer_size = tcp_size;
        self.netstack_udp_buffer_size = udp_size;
        self
    }

    /// 设置是否启用 DNS 封锁
    ///
    /// # 参数
    /// * `enable` - 是否启用 DNS 封锁
    ///
    /// # 返回值
    /// 返回修改后的配置实例，支持链式调用
    pub fn with_dns_blocking(mut self, enable: bool) -> Self {
        self.enable_dns_blocking = enable;
        self
    }

    /// 添加自定义封锁的 DNS 地址
    ///
    /// # 参数
    /// * `addresses` - 要封锁的 DNS 地址列表
    ///
    /// # 返回值
    /// 返回修改后的配置实例，支持链式调用
    pub fn with_custom_blocked_dns_addresses(mut self, addresses: Vec<String>) -> Self {
        self.custom_blocked_dns_addresses = addresses;
        self
    }

    /// 检查地址是否被封锁
    ///
    /// # 参数
    /// * `address` - 要检查的地址
    ///
    /// # 返回值
    /// 如果地址被封锁则返回 `true`，否则返回 `false`
    pub fn is_dns_address_blocked(&self, address: &str) -> bool {
        if !self.enable_dns_blocking {
            return false;
        }

        // 检查默认封锁地址
        if BLOCKED_DNS_ADDRESSES.contains(address) {
            return true;
        }

        // 检查自定义封锁地址
        self.custom_blocked_dns_addresses.iter().any(|addr| addr == address)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_default_config() {
        let config = TunConfig::default();
        assert_eq!(config.netstack_tcp_buffer_size, NETSTACK_TCP_BUFFER_SIZE);
        assert_eq!(config.netstack_udp_buffer_size, NETSTACK_UDP_BUFFER_SIZE);
        assert!(config.enable_dns_blocking);
        assert!(config.custom_blocked_dns_addresses.is_empty());
    }

    #[test]
    fn test_config_builder() {
        let config = TunConfig::new()
            .with_netstack_buffer_size(1024, 512)
            .with_dns_blocking(false);

        assert_eq!(config.netstack_tcp_buffer_size, 1024);
        assert_eq!(config.netstack_udp_buffer_size, 512);
        assert!(!config.enable_dns_blocking);
    }

    #[test]
    fn test_dns_blocking() {
        let config = TunConfig::default();

        // 测试默认封锁地址
        assert!(config.is_dns_address_blocked("*******:853"));
        assert!(config.is_dns_address_blocked("*******:443"));
        assert!(!config.is_dns_address_blocked("*******:80"));

        // 测试自定义封锁地址
        let config = config.with_custom_blocked_dns_addresses(vec!["custom.dns:853".to_string()]);
        assert!(config.is_dns_address_blocked("custom.dns:853"));
        assert!(!config.is_dns_address_blocked("other.dns:853"));

        // 测试禁用 DNS 封锁
        let config = config.with_dns_blocking(false);
        assert!(!config.is_dns_address_blocked("*******:853"));
        assert!(!config.is_dns_address_blocked("custom.dns:853"));
    }

    #[test]
    fn test_blocked_dns_addresses_set() {
        // 测试 LazyLock 初始化
        assert!(BLOCKED_DNS_ADDRESSES.contains("*******:853"));
        assert!(BLOCKED_DNS_ADDRESSES.contains("*******:443"));
        assert!(!BLOCKED_DNS_ADDRESSES.contains("*******:80"));

        // 验证集合大小
        assert_eq!(BLOCKED_DNS_ADDRESSES.len(), BLOCKED_DNS_ADDRESSES_ARRAY.len());
    }
}
